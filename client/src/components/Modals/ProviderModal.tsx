import React, { useState, useEffect, useRef } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { useModalStore } from '../stores/modalStore';
import { Search, Loader2 } from 'lucide-react';
import { ProviderBridge } from '../../utils/cepIntegration';

// Lobe Icons - Mono versions (fallback to SVG since package isn't available)
const AnthropicMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2L2 22h4l2-5h8l2 5h4L12 2zm0 6l2.5 6h-5L12 8z"/>
  </svg>
);

const OpenAIMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142-.0852 4.783-2.7582a.7712.7712 0 0 0 .7806 0l5.8428 3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zm-2.4569-16.2971a4.4755 4.4755 0 0 1 2.3445-1.9275L5.943 7.1778a.7663.7663 0 0 0 .3717.6388l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0L4.2446 9.8211a4.504 4.504 0 0 1-.7876-8.4285zm16.5618 3.8558l-5.8428-3.3685V4.4444a.0804.0804 0 0 1 .0332-.0615l4.8645-2.8077a4.4992 4.4992 0 0 1 6.6802 4.66l-.1465.0804-4.7806 2.7582a.7712.7712 0 0 0-.7806 0zm2.0107-3.0231l-.142.0852-4.7806 2.7582a.7663.7663 0 0 0-.3717.6388L9.74 4.1818l2.0201-1.1686a.0757.0757 0 0 1 .071 0l4.8076 2.7748a4.504 4.504 0 0 1 .7876 8.4285z"/>
  </svg>
);

const GoogleMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
  </svg>
);

const GroqMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
  </svg>
);

const DeepSeekMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2l10 18H2L12 2zm0 3.5L5.5 18h13L12 5.5z"/>
  </svg>
);

const MistralMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"/>
  </svg>
);

const MoonshotMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 1L9 9l-8 3 8 3 3 8 3-8 8-3-8-3-3-8z"/>
  </svg>
);

const OpenRouterMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2L2 12l10 10 10-10L12 2zm0 3.41L18.59 12 12 18.59 5.41 12 12 5.41z"/>
  </svg>
);

const PerplexityMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6H9l3-3 3 3h-2v6z"/>
  </svg>
);

const QwenMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
  </svg>
);

const TogetherMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm-6 0c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2z"/>
  </svg>
);

const VertexMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"/>
  </svg>
);

const XAIMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.36 5.64L12 12l6.36 6.36-1.41 1.41L12 14.83l-4.95 4.94-1.41-1.41L12 12 5.64 5.64l1.41-1.41L12 9.17l4.95-4.94 1.41 1.41z"/>
  </svg>
);

const OllamaMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
  </svg>
);

const LMStudioMono: React.FC<{ size?: number }> = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
  </svg>
);

const providerOptions = [
  { value: 'openai', label: 'OpenAI', icon: OpenAIMono },
  { value: 'anthropic', label: 'Anthropic', icon: AnthropicMono },
  { value: 'gemini', label: 'Google Gemini', icon: GoogleMono },
  { value: 'groq', label: 'Groq', icon: GroqMono },
  { value: 'deepseek', label: 'DeepSeek', icon: DeepSeekMono },
  { value: 'mistral', label: 'Mistral', icon: MistralMono },
  { value: 'moonshot', label: 'Moonshot AI', icon: MoonshotMono },
  { value: 'openrouter', label: 'OpenRouter', icon: OpenRouterMono },
  { value: 'perplexity', label: 'Perplexity', icon: PerplexityMono },
  { value: 'qwen', label: 'Alibaba Qwen', icon: QwenMono },
  { value: 'together', label: 'Together AI', icon: TogetherMono },
  { value: 'vertex', label: 'Google Vertex AI', icon: VertexMono },
  { value: 'xai', label: 'xAI', icon: XAIMono },
  { value: 'ollama', label: 'Ollama', icon: OllamaMono },
  { value: 'lmstudio', label: 'LM Studio', icon: LMStudioMono },
];

interface Model {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  isRecommended?: boolean;
}

export const ProviderModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const { providers, saveProviderSelection, loadModelsForProvider } = useSettingsStore();
  const [selectedProvider, setSelectedProvider] = useState('');
  const [selectedModel, setSelectedModel] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [providerSearch, setProviderSearch] = useState('');
  const [modelSearch, setModelSearch] = useState('');
  const [showProviderDropdown, setShowProviderDropdown] = useState(false);
  const [showModelDropdown, setShowModelDropdown] = useState(false);
  const providerRef = useRef<HTMLDivElement>(null);
  const modelRef = useRef<HTMLDivElement>(null);

  const filteredProviders = providerOptions.filter(provider =>
    provider.label.toLowerCase().includes(providerSearch.toLowerCase())
  );

  // Get provider state from the store
  const providerState = providers.find(p => p.id === selectedProvider);
  const models = providerState?.models || [];

  const filteredModels = models.filter(model =>
    model.name.toLowerCase().includes(modelSearch.toLowerCase())
  );

  const selectedProviderData = providerOptions.find(p => p.value === selectedProvider);

  // Trigger model loading when provider is selected and has API key
  useEffect(() => {
    if (selectedProvider && apiKey) {
      // Update the provider with the API key first, then load models
      const provider = providers.find(p => p.id === selectedProvider);
      if (provider) {
        // Update the provider config with the API key
        saveProviderSelection(selectedProvider, { apiKey });
        // Then load models
        loadModelsForProvider(selectedProvider);
      }
    }
  }, [selectedProvider, apiKey]);

  const handleProviderSelect = (providerId: string) => {
    setSelectedProvider(providerId);
    setSelectedModel('');
    setModelSearch('');
    setProviderSearch(providerOptions.find(p => p.value === providerId)?.label || '');
    setShowProviderDropdown(false);

    // Trigger model loading from the store if API key is available
    if (apiKey) {
      loadModelsForProvider(providerId);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setSelectedModel(modelId);
    const model = models.find(m => m.id === modelId);
    setModelSearch(model?.name || '');
    setShowModelDropdown(false);
  };

  const handleSave = () => {
    if (selectedProvider && selectedModel && apiKey) {
      saveProviderSelection(selectedProvider, {
        apiKey: apiKey,
        selectedModelId: selectedModel
      });
      closeModal();
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (providerRef.current && !providerRef.current.contains(event.target as Node)) {
        setShowProviderDropdown(false);
      }
      if (modelRef.current && !modelRef.current.contains(event.target as Node)) {
        setShowModelDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] shadow-2xl">
        {/* Header */}
        <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-adobe-text-primary">
              AI Provider Configuration
            </h2>
            <button
              onClick={closeModal}
              className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Provider Selector */}
          <div className="relative" ref={providerRef}>
            <label className="block text-sm font-medium text-adobe-text-primary mb-2">
              Provider
            </label>
            <div className="relative">
              <input
                type="text"
                value={providerSearch}
                onChange={(e) => {
                  setProviderSearch(e.target.value);
                  setShowProviderDropdown(true);
                }}
                onFocus={() => setShowProviderDropdown(true)}
                placeholder="Search providers..."
                className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10"
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary" size={18} />
            </div>

            {showProviderDropdown && (
              <div className="absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto">
                {filteredProviders.map((provider) => {
                  const IconComponent = provider.icon;
                  return (
                    <div
                      key={provider.value}
                      className="px-4 py-3 cursor-pointer flex items-center space-x-3 hover:bg-adobe-bg-tertiary text-adobe-text-primary"
                      onClick={() => handleProviderSelect(provider.value)}
                    >
                      <IconComponent size={16} />
                      <span className="font-medium">{provider.label}</span>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Model Selector */}
          <div className="relative" ref={modelRef}>
            <label className="block text-sm font-medium text-adobe-text-primary mb-2">
              Model
            </label>
            <div className="relative">
              <input
                type="text"
                value={modelSearch}
                onChange={(e) => {
                  setModelSearch(e.target.value);
                  setShowModelDropdown(true);
                }}
                onFocus={() => selectedProvider && setShowModelDropdown(true)}
                placeholder={selectedProvider ? "Search models..." : "Select a provider first"}
                disabled={!selectedProvider}
                className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10 disabled:opacity-50"
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary" size={18} />
              {providerState?.isLoading && (
                <Loader2 className="absolute right-10 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary animate-spin" size={16} />
              )}
            </div>

            {showModelDropdown && selectedProvider && (
              <div className="absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto">
                {providerState?.isLoading && models.length === 0 && (
                  <div className="px-4 py-3 text-adobe-text-secondary flex items-center space-x-2">
                    <Loader2 className="animate-spin" size={16} />
                    <span>Loading models...</span>
                  </div>
                )}

                {providerState?.error && models.length === 0 && (
                  <div className="px-4 py-3 text-adobe-text-secondary">
                    {providerState.error}
                  </div>
                )}
                
                {filteredModels.map((model) => (
                  <div
                    key={model.id}
                    className="px-4 py-3 cursor-pointer hover:bg-adobe-bg-tertiary text-adobe-text-primary"
                    onClick={() => handleModelSelect(model.id)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{model.name}</span>
                      {model.isRecommended && (
                        <span className="text-xs bg-adobe-accent/20 text-adobe-accent px-2 py-1 rounded">
                          Recommended
                        </span>
                      )}
                    </div>
                    {model.description && (
                      <p className="text-xs text-adobe-text-secondary mt-1">{model.description}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* API Key Input */}
          <div>
            <label className="block text-sm font-medium text-adobe-text-primary mb-2">
              API Key
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter your API key"
              className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between">
          <button
            onClick={closeModal}
            className="px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!selectedProvider || !selectedModel || !apiKey}
            className="px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors"
          >
            Save & Close
          </button>
        </div>
      </div>
    </div>
  );
};
